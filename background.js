// Background script for Word Tracker Extension
// Handles tab capture and screenshot functionality

console.log('Word Tracker background script loaded');

// Handle messages from content script and popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'captureTab') {
    handleTabCapture(request, sender, sendResponse);
    return true; // Keep message channel open for async response
  }
  
  if (request.action === 'captureVisibleTab') {
    handleVisibleTabCapture(request, sender, sendResponse);
    return true;
  }
});

// Capture entire tab
async function handleTabCapture(request, sender, sendResponse) {
  try {
    const tabId = sender.tab.id;
    
    // Capture visible tab
    const dataUrl = await chrome.tabs.captureVisibleTab(null, {
      format: 'png',
      quality: 100
    });
    
    sendResponse({
      success: true,
      dataUrl: dataUrl
    });
  } catch (error) {
    console.error('Tab capture failed:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// Capture visible tab with specific options
async function handleVisibleTabCapture(request, sender, sendResponse) {
  try {
    const options = request.options || {
      format: 'png',
      quality: 100
    };
    
    const dataUrl = await chrome.tabs.captureVisibleTab(null, options);
    
    sendResponse({
      success: true,
      dataUrl: dataUrl
    });
  } catch (error) {
    console.error('Visible tab capture failed:', error);
    sendResponse({
      success: false,
      error: error.message
    });
  }
}

// Handle extension installation
chrome.runtime.onInstalled.addListener((details) => {
  console.log('Word Tracker extension installed:', details.reason);
  
  if (details.reason === 'install') {
    // Set default values
    chrome.storage.local.set({
      timeout: 5,
      targetWord: '',
      selectedArea: null,
      ocrEnabled: true
    });
  }
});

// Handle tab updates (optional - for future features)
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // Tab finished loading - could be used for auto-tracking
    console.log('Tab updated:', tab.url);
  }
});
