#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');
const archiver = require('archiver');
const { execSync } = require('child_process');

// Configuration
const CONFIG = {
  sourceDir: '.',
  buildDir: 'dist',
  packageName: 'word-tracker-extension',
  filesToCopy: [
    'manifest.json',
    'popup.html',
    'popup.js',
    'content.js',
    'images'
  ],
  filesToExclude: [
    'node_modules',
    'dist',
    'build.js',
    'create-icons.js',
    'package.json',
    'package-lock.json',
    '.git',
    '.gitignore',
    'README.md'
  ]
};

// Parse command line arguments
const args = process.argv.slice(2);
const isDev = args.includes('--dev');
const isProd = args.includes('--prod');
const shouldClean = args.includes('--clean');
const shouldPackage = args.includes('--package');

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
  const prefix = {
    info: '📦',
    success: '✅',
    error: '❌',
    warning: '⚠️'
  }[type];
  console.log(`[${timestamp}] ${prefix} ${message}`);
}

function ensureDirectoryExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    log(`Created directory: ${dir}`);
  }
}

function cleanBuildDirectory() {
  if (fs.existsSync(CONFIG.buildDir)) {
    fs.removeSync(CONFIG.buildDir);
    log(`Cleaned build directory: ${CONFIG.buildDir}`, 'success');
  }
}

function validateManifest() {
  const manifestPath = path.join(CONFIG.sourceDir, 'manifest.json');
  if (!fs.existsSync(manifestPath)) {
    throw new Error('manifest.json not found');
  }
  
  const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
  
  // Basic validation
  if (!manifest.manifest_version) {
    throw new Error('manifest.json missing manifest_version');
  }
  if (!manifest.name) {
    throw new Error('manifest.json missing name');
  }
  if (!manifest.version) {
    throw new Error('manifest.json missing version');
  }
  
  log('Manifest validation passed', 'success');
  return manifest;
}

function createIcons() {
  log('Creating icons...');
  try {
    execSync('node create-icons.js', { stdio: 'inherit' });
    log('Icons created successfully', 'success');
  } catch (error) {
    log('Failed to create icons: ' + error.message, 'error');
    throw error;
  }
}

function copyFiles() {
  log('Copying files to build directory...');
  
  CONFIG.filesToCopy.forEach(file => {
    const sourcePath = path.join(CONFIG.sourceDir, file);
    const destPath = path.join(CONFIG.buildDir, file);
    
    if (fs.existsSync(sourcePath)) {
      fs.copySync(sourcePath, destPath);
      log(`Copied: ${file}`);
    } else {
      log(`Warning: ${file} not found, skipping`, 'warning');
    }
  });
  
  log('File copying completed', 'success');
}

function optimizeForProduction(manifest) {
  if (!isProd) return manifest;
  
  log('Optimizing for production...');
  
  // Remove development-specific fields if any
  const prodManifest = { ...manifest };
  
  // You can add production optimizations here
  // For example, minifying content scripts, removing debug info, etc.
  
  // Write optimized manifest
  const manifestPath = path.join(CONFIG.buildDir, 'manifest.json');
  fs.writeFileSync(manifestPath, JSON.stringify(prodManifest, null, 2));
  
  log('Production optimization completed', 'success');
  return prodManifest;
}

function createPackage() {
  return new Promise((resolve, reject) => {
    const packagePath = path.join('.', `${CONFIG.packageName}-v${getVersion()}.zip`);
    const output = fs.createWriteStream(packagePath);
    const archive = archiver('zip', { zlib: { level: 9 } });
    
    output.on('close', () => {
      log(`Package created: ${packagePath} (${archive.pointer()} bytes)`, 'success');
      resolve(packagePath);
    });
    
    archive.on('error', (err) => {
      log(`Package creation failed: ${err.message}`, 'error');
      reject(err);
    });
    
    archive.pipe(output);
    archive.directory(CONFIG.buildDir, false);
    archive.finalize();
  });
}

function getVersion() {
  const manifest = JSON.parse(fs.readFileSync(path.join(CONFIG.sourceDir, 'manifest.json'), 'utf8'));
  return manifest.version;
}

function displayBuildInfo(manifest) {
  log('='.repeat(50));
  log(`Extension: ${manifest.name}`);
  log(`Version: ${manifest.version}`);
  log(`Build Mode: ${isProd ? 'Production' : isDev ? 'Development' : 'Default'}`);
  log(`Build Directory: ${CONFIG.buildDir}`);
  log('='.repeat(50));
}

// Main build function
async function build() {
  try {
    log('Starting build process...');
    
    // Clean if requested
    if (shouldClean) {
      cleanBuildDirectory();
      if (args.length === 1) {
        log('Clean completed', 'success');
        return;
      }
    }
    
    // Validate manifest
    const manifest = validateManifest();
    displayBuildInfo(manifest);
    
    // Create build directory
    ensureDirectoryExists(CONFIG.buildDir);
    
    // Create icons
    createIcons();
    
    // Copy files
    copyFiles();
    
    // Optimize for production if needed
    optimizeForProduction(manifest);
    
    // Create package if requested
    if (shouldPackage) {
      await createPackage();
    }
    
    log('Build completed successfully! 🎉', 'success');
    log(`Build output: ${path.resolve(CONFIG.buildDir)}`);
    
  } catch (error) {
    log(`Build failed: ${error.message}`, 'error');
    process.exit(1);
  }
}

// Help function
function showHelp() {
  console.log(`
Word Tracker Extension Build Script

Usage: node build.js [options]

Options:
  --dev       Development build
  --prod      Production build (optimized)
  --clean     Clean build directory
  --package   Create ZIP package for Chrome Web Store
  --help      Show this help message

Examples:
  node build.js                    # Default build
  node build.js --dev              # Development build
  node build.js --prod --package   # Production build with packaging
  node build.js --clean            # Clean build directory only
`);
}

// Main execution
if (args.includes('--help')) {
  showHelp();
} else {
  build();
}
