{"name": "word-tracker-extension", "version": "1.0.0", "description": "Chrome extension to track occurrences of target words with configurable timeout", "main": "index.js", "scripts": {"build": "node build.js", "build:icons": "node create-icons.js", "build:dev": "node build.js --dev", "build:prod": "node build.js --prod", "clean": "node build.js --clean", "package": "node build.js --package", "test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"fs-extra": "^11.1.1", "archiver": "^5.3.1"}, "dependencies": {"tesseract.js": "^4.1.1"}, "author": "", "license": "ISC"}