// Simple icon creation script that creates SVG icons and converts them to base64 data URLs
// This avoids the need for canvas package and system dependencies
const fs = require('fs-extra');

// Create icons directory
fs.ensureDirSync('images');

// SVG template for the icon
function createSVGIcon(size) {
  return `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="${size}" height="${size}" fill="#4285f4"/>
  <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="${size * 0.6}" font-weight="bold"
        fill="white" text-anchor="middle" dominant-baseline="central">W</text>
</svg>`;
}

// Create a simple PNG-like file (actually SVG saved as .png for simplicity)
// In a real scenario, you'd want to use proper PNG files or convert SVG to PNG
[16, 48, 128].forEach(size => {
  const svgContent = createSVGI<PERSON>(size);

  // For now, we'll create SVG files with .png extension
  // This is a workaround - in production you'd want actual PNG files
  fs.writeFileSync(`images/icon${size}.svg`, svgContent);

  // Create a simple colored square as a fallback PNG (base64 encoded)
  // This creates a minimal PNG file structure
  const pngData = createMinimalPNG(size);
  fs.writeFileSync(`images/icon${size}.png`, pngData);

  console.log(`Created icon${size}.png and icon${size}.svg`);
});

// Creates a minimal PNG file (very basic implementation)
function createMinimalPNG(size) {
  // This is a simplified approach - creates a basic colored square
  // For production, you'd want to use a proper image library or pre-made icons

  // Create a simple base64 encoded PNG (1x1 blue pixel, scaled)
  const base64PNG = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';
  return Buffer.from(base64PNG, 'base64');
}

console.log('Icon creation completed! Note: For production use, replace with proper PNG icons.');