// Run this with Node.js to create placeholder icons
// npm install canvas fs-extra
const { createCanvas } = require('canvas');
const fs = require('fs-extra');

// Create icons directory
fs.ensureDirSync('images');

// Create icons of different sizes
[16, 48, 128].forEach(size => {
  const canvas = createCanvas(size, size);
  const ctx = canvas.getContext('2d');
  
  // Draw background
  ctx.fillStyle = '#4285f4';
  ctx.fillRect(0, 0, size, size);
  
  // Draw "W" letter
  ctx.fillStyle = 'white';
  ctx.font = `bold ${size * 0.6}px Arial`;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText('W', size / 2, size / 2);
  
  // Save as PNG
  const buffer = canvas.toBuffer('image/png');
  fs.writeFileSync(`images/icon${size}.png`, buffer);
  
  console.log(`Created icon${size}.png`);
});