<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word Tracker Extension Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: #4285f4;
            color: white;
            padding: 20px;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .highlight-word {
            background-color: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .sidebar {
            float: right;
            width: 300px;
            background: #e3f2fd;
            padding: 15px;
            margin: 0 0 20px 20px;
            border-radius: 5px;
        }
        .content-area {
            overflow: hidden;
        }
        .footer {
            background: #f0f0f0;
            padding: 20px;
            margin: 20px -20px -20px -20px;
            border-radius: 0 0 8px 8px;
            text-align: center;
        }
        .demo-instructions {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Word Tracker Extension Demo Page</h1>
            <p>Use this page to test the area selection functionality of the Word Tracker extension.</p>
        </div>

        <div class="demo-instructions">
            <h3>🎯 How to Test the Area Selector:</h3>
            <ol>
                <li>Install the Word Tracker extension in Chrome</li>
                <li>Click the extension icon to open the popup</li>
                <li>Enter a target word (try "test", "demo", or "example")</li>
                <li>Click "Select Area" and drag to select a specific section below</li>
                <li>Click "Start Tracking" to see word counts in the selected area only</li>
                <li>Try selecting different areas and compare the results!</li>
            </ol>
        </div>

        <div class="content-area">
            <div class="sidebar">
                <h3>Sidebar Content</h3>
                <p>This is a <span class="highlight-word">test</span> sidebar with some example content. The word "demo" appears here too.</p>
                <p>You can select this area specifically to track words only in the sidebar.</p>
                <ul>
                    <li>Example item 1</li>
                    <li>Test item 2</li>
                    <li>Demo item 3</li>
                </ul>
            </div>

            <div class="section">
                <h3>Section 1: Introduction</h3>
                <p>This is a <span class="highlight-word">test</span> section containing various words for demonstration purposes. The word "example" appears multiple times in this demo page.</p>
                <p>You can use the area selector to focus on specific sections and track word occurrences within those areas only.</p>
            </div>

            <div class="section">
                <h3>Section 2: Features Demo</h3>
                <p>The Word Tracker extension includes an advanced area selector that allows you to:</p>
                <ul>
                    <li>Select specific regions of a webpage</li>
                    <li>Track words within those regions only</li>
                    <li>Save area selections across sessions</li>
                    <li>Clear selections to search the full page</li>
                </ul>
                <p>This <span class="highlight-word">test</span> demonstrates how the area selector works with different content types.</p>
            </div>

            <div class="section">
                <h3>Section 3: Example Content</h3>
                <p>Here's another example section with more test content. The word "demo" appears frequently throughout this page.</p>
                <p>Try selecting just this section and tracking the word "example" - you should see different counts compared to selecting the entire page.</p>
                <blockquote>
                    "This is an example quote that contains the word test multiple times for demonstration purposes."
                </blockquote>
            </div>

            <div class="section">
                <h3>Section 4: Technical Details</h3>
                <p>The area selector uses advanced DOM traversal techniques to search within selected boundaries.</p>
                <p>Key features include:</p>
                <ul>
                    <li><strong>Visual Selection</strong>: Click and drag interface</li>
                    <li><strong>Precise Boundaries</strong>: Accurate area detection</li>
                    <li><strong>Performance</strong>: Efficient text searching</li>
                    <li><strong>Persistence</strong>: Saved selections</li>
                </ul>
                <p>This example shows how the extension can handle complex layouts and nested content.</p>
            </div>
        </div>

        <div class="footer">
            <p>Word Tracker Extension Demo - Test the area selector functionality!</p>
            <p>Words to try: "test", "demo", "example", "area", "selector", "extension"</p>
        </div>
    </div>

    <script>
        // Add some dynamic content for testing
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Demo page loaded - ready for Word Tracker extension testing');
            
            // Add a counter to show how many times certain words appear
            const words = ['test', 'demo', 'example'];
            words.forEach(word => {
                const count = (document.body.innerText.match(new RegExp('\\b' + word + '\\b', 'gi')) || []).length;
                console.log(`Word "${word}" appears ${count} times on this page`);
            });
        });
    </script>
</body>
</html>
