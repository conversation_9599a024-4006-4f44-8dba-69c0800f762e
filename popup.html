<!DOCTYPE html>
<html>
<head>
  <title>Word Tracker</title>
  <style>
    body {
      width: 300px;
      padding: 10px;
      font-family: Arial, sans-serif;
    }
    .form-group {
      margin-bottom: 10px;
    }
    label {
      display: block;
      margin-bottom: 5px;
    }
    input {
      width: 100%;
      padding: 5px;
      box-sizing: border-box;
    }
    #logWindow {
      width: 100%;
      height: 200px;
      border: 1px solid #ccc;
      padding: 5px;
      margin-top: 10px;
      overflow-y: auto;
      background-color: #f9f9f9;
      font-family: monospace;
    }
    button {
      padding: 8px 15px;
      background-color: #4285f4;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #3367d6;
    }
  </style>
</head>
<body>
  <h2>Word Tracker</h2>
  
  <div class="form-group">
    <label for="timeout">Timeout (seconds):</label>
    <input type="number" id="timeout" min="1" value="5">
  </div>
  
  <div class="form-group">
    <label for="targetWord">Target Word:</label>
    <input type="text" id="targetWord" placeholder="Enter word to track">
  </div>
  
  <button id="startTracking">Start Tracking</button>
  <button id="stopTracking">Stop Tracking</button>
  
  <div class="form-group">
    <label for="logWindow">Log:</label>
    <div id="logWindow"></div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>