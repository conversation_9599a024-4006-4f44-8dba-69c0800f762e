<!DOCTYPE html>
<html>
<head>
  <title>Word Tracker</title>
  <style>
    body {
      width: 300px;
      padding: 10px;
      font-family: Arial, sans-serif;
    }
    .form-group {
      margin-bottom: 10px;
    }
    label {
      display: block;
      margin-bottom: 5px;
    }
    input {
      width: 100%;
      padding: 5px;
      box-sizing: border-box;
    }
    #logWindow {
      width: 100%;
      height: 200px;
      border: 1px solid #ccc;
      padding: 5px;
      margin-top: 10px;
      overflow-y: auto;
      background-color: #f9f9f9;
      font-family: monospace;
    }
    button {
      padding: 8px 15px;
      background-color: #4285f4;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #3367d6;
    }
    button:disabled {
      background-color: #ccc;
      cursor: not-allowed;
    }
    .area-selector {
      display: flex;
      align-items: center;
      gap: 10px;
    }
    .area-info {
      font-size: 12px;
      color: #666;
      margin-top: 5px;
    }
    .area-status {
      padding: 3px 8px;
      border-radius: 3px;
      font-size: 11px;
      font-weight: bold;
    }
    .area-status.selected {
      background-color: #e8f5e8;
      color: #2e7d32;
    }
    .area-status.none {
      background-color: #fff3e0;
      color: #f57c00;
    }
  </style>
</head>
<body>
  <h2>Word Tracker</h2>
  
  <div class="form-group">
    <label for="timeout">Timeout (seconds):</label>
    <input type="number" id="timeout" min="1" value="5">
  </div>
  
  <div class="form-group">
    <label for="targetWord">Target Word:</label>
    <input type="text" id="targetWord" placeholder="Enter word to track">
  </div>

  <div class="form-group">
    <label for="areaSelector">Search Area:</label>
    <div class="area-selector">
      <button id="selectAreaBtn" type="button">Select Area</button>
      <button id="clearAreaBtn" type="button">Clear</button>
    </div>
    <div class="area-info">
      <span class="area-status none" id="areaStatus">No area selected (full page)</span>
    </div>
  </div>

  <button id="startTracking">Start Tracking</button>
  <button id="stopTracking">Stop Tracking</button>
  
  <div class="form-group">
    <label for="logWindow">Log:</label>
    <div id="logWindow"></div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>