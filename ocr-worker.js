// OCR Worker for Word Detection using Tesseract.js
// This file handles OCR processing in a separate context to avoid blocking the main thread

// Import Tesseract.js (will be loaded via script tag in content script)
// Note: In a Chrome extension, we need to load Tesseract differently

class OCRWordDetector {
  constructor() {
    this.isInitialized = false;
    this.worker = null;
    this.initPromise = null;
  }

  // Initialize Tesseract worker
  async initialize() {
    if (this.isInitialized) return;
    if (this.initPromise) return this.initPromise;

    this.initPromise = this._initializeWorker();
    await this.initPromise;
    this.isInitialized = true;
  }

  async _initializeWorker() {
    try {
      // Load Tesseract.js from CDN
      if (typeof Tesseract === 'undefined') {
        await this.loadTesseract();
      }

      // Create worker
      this.worker = await Tesseract.createWorker('eng', 1, {
        logger: m => {
          if (m.status === 'recognizing text') {
            console.log(`OCR Progress: ${Math.round(m.progress * 100)}%`);
          }
        }
      });

      console.log('OCR Worker initialized successfully');
    } catch (error) {
      console.error('Failed to initialize <PERSON>CR worker:', error);
      throw error;
    }
  }

  // Load Tesseract.js library
  loadTesseract() {
    return new Promise((resolve, reject) => {
      if (typeof Tesseract !== 'undefined') {
        resolve();
        return;
      }

      // For Chrome extension, we'll use a simpler approach
      // Since we can't load external scripts, we'll implement a basic OCR simulation
      // In a real implementation, you would bundle Tesseract.js with the extension
      console.warn('Tesseract.js not available, using fallback OCR simulation');

      // Create a mock Tesseract object for demonstration
      window.Tesseract = {
        createWorker: async (lang, oem, options) => {
          return {
            recognize: async (imageData) => {
              // This is a simulation - in real implementation, use actual OCR
              return await this.simulateOCR(imageData);
            },
            terminate: async () => {
              console.log('Mock OCR worker terminated');
            }
          };
        }
      };

      resolve();
    });
  }

  // Simulate OCR for demonstration (replace with actual OCR in production)
  async simulateOCR(imageData) {
    // This is a placeholder that simulates OCR results
    // In a real implementation, you would:
    // 1. Bundle Tesseract.js with the extension
    // 2. Or use a server-side OCR API
    // 3. Or implement a different client-side OCR solution

    console.log('Simulating OCR processing...');

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Return mock OCR results
    return {
      data: {
        text: 'This is simulated OCR text. In a real implementation, this would contain the actual text extracted from the image.',
        confidence: 85,
        words: [
          { text: 'This', confidence: 90, bbox: { x0: 10, y0: 10, x1: 50, y1: 30 } },
          { text: 'is', confidence: 95, bbox: { x0: 55, y0: 10, x1: 70, y1: 30 } },
          { text: 'simulated', confidence: 80, bbox: { x0: 75, y0: 10, x1: 150, y1: 30 } },
          { text: 'OCR', confidence: 85, bbox: { x0: 155, y0: 10, x1: 185, y1: 30 } },
          { text: 'text', confidence: 90, bbox: { x0: 190, y0: 10, x1: 220, y1: 30 } }
        ]
      }
    };
  }

  // Perform OCR on image data
  async recognizeText(imageData) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const { data: { text, confidence, words } } = await this.worker.recognize(imageData);
      
      return {
        text: text.trim(),
        confidence: confidence,
        words: words.map(word => ({
          text: word.text,
          confidence: word.confidence,
          bbox: word.bbox
        }))
      };
    } catch (error) {
      console.error('OCR recognition failed:', error);
      throw error;
    }
  }

  // Find specific word in recognized text
  findWordInText(recognizedData, targetWord) {
    const { text, words } = recognizedData;
    
    // Case-insensitive search
    const regex = new RegExp('\\b' + targetWord.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '\\b', 'gi');
    const textMatches = text.match(regex) || [];
    
    // Find word-level matches with bounding boxes
    const wordMatches = words.filter(word => 
      regex.test(word.text) && word.confidence > 60 // Filter low-confidence matches
    );

    return {
      count: textMatches.length,
      matches: textMatches,
      wordDetails: wordMatches,
      fullText: text,
      confidence: recognizedData.confidence
    };
  }

  // Cleanup worker
  async terminate() {
    if (this.worker) {
      await this.worker.terminate();
      this.worker = null;
      this.isInitialized = false;
      this.initPromise = null;
    }
  }
}

// Screenshot capture utilities
class ScreenshotCapture {
  // Capture screenshot of specific area
  static async captureArea(area) {
    try {
      // Use Chrome's tab capture API
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      // Set canvas size to match the selected area
      canvas.width = area.width;
      canvas.height = area.height;

      // Use html2canvas-like approach for area capture
      const screenshot = await this.captureVisibleArea(area);
      
      return screenshot;
    } catch (error) {
      console.error('Failed to capture screenshot:', error);
      throw error;
    }
  }

  // Capture visible area using canvas
  static async captureVisibleArea(area) {
    return new Promise((resolve, reject) => {
      // Create a canvas to capture the area
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      canvas.width = area.width;
      canvas.height = area.height;

      // Use html2canvas approach - render DOM elements to canvas
      this.renderDOMToCanvas(area, canvas, ctx)
        .then(() => {
          // Convert canvas to blob
          canvas.toBlob((blob) => {
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('Failed to create screenshot blob'));
            }
          }, 'image/png');
        })
        .catch(reject);
    });
  }

  // Render DOM elements within area to canvas
  static async renderDOMToCanvas(area, canvas, ctx) {
    try {
      // Get all elements within the selected area
      const elements = this.getElementsInArea(area);
      
      // Set white background
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Render each element
      for (const element of elements) {
        await this.renderElementToCanvas(element, area, ctx);
      }
    } catch (error) {
      console.error('Failed to render DOM to canvas:', error);
      throw error;
    }
  }

  // Get elements within the specified area
  static getElementsInArea(area) {
    const elements = [];
    const walker = document.createTreeWalker(
      document.body,
      NodeFilter.SHOW_ELEMENT,
      {
        acceptNode: (node) => {
          const rect = node.getBoundingClientRect();
          const isInArea = rect.left < area.left + area.width &&
                          rect.right > area.left &&
                          rect.top < area.top + area.height &&
                          rect.bottom > area.top;
          return isInArea ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;
        }
      }
    );

    let node;
    while (node = walker.nextNode()) {
      elements.push(node);
    }

    return elements;
  }

  // Render individual element to canvas
  static async renderElementToCanvas(element, area, ctx) {
    const rect = element.getBoundingClientRect();
    const style = window.getComputedStyle(element);
    
    // Calculate position relative to selected area
    const x = rect.left - area.left;
    const y = rect.top - area.top;
    
    // Only render if element is visible and has content
    if (style.display === 'none' || style.visibility === 'hidden') {
      return;
    }

    // Render text content
    if (element.textContent && element.textContent.trim()) {
      ctx.fillStyle = style.color || 'black';
      ctx.font = `${style.fontSize} ${style.fontFamily}`;
      
      // Simple text rendering (this is a basic implementation)
      const text = element.textContent.trim();
      if (text && x >= 0 && y >= 0 && x < area.width && y < area.height) {
        ctx.fillText(text, Math.max(0, x), Math.max(0, y + 12)); // Basic text positioning
      }
    }
  }

  // Alternative: Use Chrome's tab capture API (requires background script)
  static async captureTab() {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({
        action: 'captureTab'
      }, (response) => {
        if (response.success) {
          resolve(response.dataUrl);
        } else {
          reject(new Error(response.error));
        }
      });
    });
  }
}

// Export for use in content script
if (typeof window !== 'undefined') {
  window.OCRWordDetector = OCRWordDetector;
  window.ScreenshotCapture = ScreenshotCapture;
}
