// DOM elements
const timeoutInput = document.getElementById('timeout');
const targetWordInput = document.getElementById('targetWord');
const startButton = document.getElementById('startTracking');
const stopButton = document.getElementById('stopTracking');
const logWindow = document.getElementById('logWindow');
const selectAreaBtn = document.getElementById('selectAreaBtn');
const clearAreaBtn = document.getElementById('clearAreaBtn');
const areaStatus = document.getElementById('areaStatus');

// Tracking state
let isTracking = false;
let trackingInterval = null;
let selectedArea = null;

// Load saved values
chrome.storage.local.get(['timeout', 'targetWord', 'selectedArea'], (result) => {
  if (result.timeout) timeoutInput.value = result.timeout;
  if (result.targetWord) targetWordInput.value = result.targetWord;
  if (result.selectedArea) {
    selectedArea = result.selectedArea;
    updateAreaStatus();
  }
});

// Log function
function log(message) {
  const timestamp = new Date().toLocaleTimeString();
  logWindow.innerHTML += `[${timestamp}] ${message}<br>`;
  logWindow.scrollTop = logWindow.scrollHeight;
}

// Update area status display
function updateAreaStatus() {
  if (selectedArea) {
    areaStatus.textContent = `Area selected (${Math.round(selectedArea.width)}×${Math.round(selectedArea.height)}px)`;
    areaStatus.className = 'area-status selected';
  } else {
    areaStatus.textContent = 'No area selected (full page)';
    areaStatus.className = 'area-status none';
  }
}

// Area selection functionality
selectAreaBtn.addEventListener('click', async () => {
  try {
    selectAreaBtn.disabled = true;
    selectAreaBtn.textContent = 'Selecting...';

    const [tab] = await chrome.tabs.query({active: true, currentWindow: true});

    const response = await chrome.tabs.sendMessage(tab.id, {
      action: 'startAreaSelection'
    });

    if (response.success && response.area) {
      selectedArea = response.area;
      chrome.storage.local.set({ selectedArea });
      updateAreaStatus();
      log('Area selected successfully');
    } else {
      log('Area selection cancelled');
    }
  } catch (error) {
    log(`Error selecting area: ${error.message}`);
  } finally {
    selectAreaBtn.disabled = false;
    selectAreaBtn.textContent = 'Select Area';
  }
});

// Clear area selection
clearAreaBtn.addEventListener('click', async () => {
  try {
    selectedArea = null;
    chrome.storage.local.remove('selectedArea');
    updateAreaStatus();

    const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
    await chrome.tabs.sendMessage(tab.id, {
      action: 'clearAreaSelection'
    });

    log('Area selection cleared');
  } catch (error) {
    log(`Error clearing area: ${error.message}`);
  }
});

// Start tracking
startButton.addEventListener('click', () => {
  const timeout = parseInt(timeoutInput.value);
  const targetWord = targetWordInput.value.trim();
  
  // Validate inputs
  if (!timeout || timeout < 1) {
    log('Error: Timeout must be at least 1 second');
    return;
  }
  
  if (!targetWord) {
    log('Error: Target word cannot be empty');
    return;
  }
  
  // Save values
  chrome.storage.local.set({ timeout, targetWord, selectedArea });
  
  // Start tracking
  isTracking = true;
  const areaText = selectedArea ? `in selected area (${Math.round(selectedArea.width)}×${Math.round(selectedArea.height)}px)` : 'on full page';
  log(`Started tracking for word "${targetWord}" every ${timeout} seconds ${areaText}`);
  
  // Execute screenshot-based word detection
  function checkForWord() {
    chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
      if (tabs.length === 0) return;

      // Use screenshot-based OCR detection
      chrome.tabs.sendMessage(tabs[0].id, {
        action: 'detectWordsInScreenshot',
        targetWord: targetWord,
        selectedArea: selectedArea
      }, (result) => {
        if (chrome.runtime.lastError) {
          log(`Error: ${chrome.runtime.lastError.message}`);
          return;
        }

        if (result.success) {
          const areaText = result.area || 'unknown area';
          const confidenceText = result.confidence ? ` (${Math.round(result.confidence)}% confidence)` : '';
          log(`Found ${result.count} occurrences of "${targetWord}" in ${areaText}${confidenceText}`);

          // Log additional details if available
          if (result.wordDetails && result.wordDetails.length > 0) {
            const avgConfidence = result.wordDetails.reduce((sum, word) => sum + word.confidence, 0) / result.wordDetails.length;
            log(`Average word confidence: ${Math.round(avgConfidence)}%`);
          }
        } else {
          log(`OCR detection failed: ${result.error}`);
          // Fallback to DOM-based search
          fallbackToDOMSearch(tabs[0].id);
        }
      });
    });
  }

  // Fallback to DOM-based search if OCR fails
  function fallbackToDOMSearch(tabId) {
    chrome.scripting.executeScript({
      target: {tabId: tabId},
      function: findWordOnPage,
      args: [targetWord, selectedArea]
    }, (results) => {
      if (chrome.runtime.lastError) {
        log(`Fallback search error: ${chrome.runtime.lastError.message}`);
        return;
      }

      const result = results[0].result;
      log(`Fallback DOM search: Found ${result.count} occurrences of "${targetWord}"`);
    });
  }
  
  // Run immediately and then on interval
  checkForWord();
  trackingInterval = setInterval(checkForWord, timeout * 1000);
});

// Stop tracking
stopButton.addEventListener('click', () => {
  if (trackingInterval) {
    clearInterval(trackingInterval);
    trackingInterval = null;
    isTracking = false;
    log('Tracking stopped');
  }
});

// Function to be injected into the page
function findWordOnPage(targetWord, selectedArea) {
  let searchText;

  if (selectedArea) {
    // Search within selected area
    const elements = document.elementsFromPoint(
      selectedArea.left + selectedArea.width / 2,
      selectedArea.top + selectedArea.height / 2
    );

    // Find elements within the selected area bounds
    const elementsInArea = [];
    const walker = document.createTreeWalker(
      document.body,
      NodeFilter.SHOW_ELEMENT,
      {
        acceptNode: function(node) {
          const rect = node.getBoundingClientRect();
          const isInArea = rect.left < selectedArea.left + selectedArea.width &&
                          rect.right > selectedArea.left &&
                          rect.top < selectedArea.top + selectedArea.height &&
                          rect.bottom > selectedArea.top;
          return isInArea ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT;
        }
      }
    );

    let node;
    const textNodes = [];
    while (node = walker.nextNode()) {
      // Get text nodes within this element
      const textWalker = document.createTreeWalker(
        node,
        NodeFilter.SHOW_TEXT,
        null
      );
      let textNode;
      while (textNode = textWalker.nextNode()) {
        textNodes.push(textNode.textContent);
      }
    }

    searchText = textNodes.join(' ');
  } else {
    // Search entire page
    searchText = document.body.innerText;
  }

  const regex = new RegExp('\\b' + targetWord + '\\b', 'gi');
  const matches = searchText.match(regex) || [];

  return {
    count: matches.length,
    matches: matches,
    searchArea: selectedArea ? 'selected area' : 'full page'
  };
}

// Initialize area status on popup load
updateAreaStatus();