// DOM elements
const timeoutInput = document.getElementById('timeout');
const targetWordInput = document.getElementById('targetWord');
const startButton = document.getElementById('startTracking');
const stopButton = document.getElementById('stopTracking');
const logWindow = document.getElementById('logWindow');

// Tracking state
let isTracking = false;
let trackingInterval = null;

// Load saved values
chrome.storage.local.get(['timeout', 'targetWord'], (result) => {
  if (result.timeout) timeoutInput.value = result.timeout;
  if (result.targetWord) targetWordInput.value = result.targetWord;
});

// Log function
function log(message) {
  const timestamp = new Date().toLocaleTimeString();
  logWindow.innerHTML += `[${timestamp}] ${message}<br>`;
  logWindow.scrollTop = logWindow.scrollHeight;
}

// Start tracking
startButton.addEventListener('click', () => {
  const timeout = parseInt(timeoutInput.value);
  const targetWord = targetWordInput.value.trim();
  
  // Validate inputs
  if (!timeout || timeout < 1) {
    log('Error: Timeout must be at least 1 second');
    return;
  }
  
  if (!targetWord) {
    log('Error: Target word cannot be empty');
    return;
  }
  
  // Save values
  chrome.storage.local.set({ timeout, targetWord });
  
  // Start tracking
  isTracking = true;
  log(`Started tracking for word "${targetWord}" every ${timeout} seconds`);
  
  // Execute content script to check for the word
  function checkForWord() {
    chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
      if (tabs.length === 0) return;
      
      chrome.scripting.executeScript({
        target: {tabId: tabs[0].id},
        function: findWordOnPage,
        args: [targetWord]
      }, (results) => {
        if (chrome.runtime.lastError) {
          log(`Error: ${chrome.runtime.lastError.message}`);
          return;
        }
        
        const result = results[0].result;
        log(`Found ${result.count} occurrences of "${targetWord}"`);
      });
    });
  }
  
  // Run immediately and then on interval
  checkForWord();
  trackingInterval = setInterval(checkForWord, timeout * 1000);
});

// Stop tracking
stopButton.addEventListener('click', () => {
  if (trackingInterval) {
    clearInterval(trackingInterval);
    trackingInterval = null;
    isTracking = false;
    log('Tracking stopped');
  }
});

// Function to be injected into the page
function findWordOnPage(targetWord) {
  const bodyText = document.body.innerText;
  const regex = new RegExp('\\b' + targetWord + '\\b', 'gi');
  const matches = bodyText.match(regex) || [];
  return {
    count: matches.length,
    matches: matches
  };
}