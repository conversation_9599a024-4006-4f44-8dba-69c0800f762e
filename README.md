# Word Tracker Chrome Extension

A Chrome extension that tracks occurrences of target words with configurable timeout.

## Features

- **Screenshot-based OCR**: Uses optical character recognition to detect words in images, canvas, and visual content
- **Area Selection**: Select specific areas of the page to search within
- **Visual Word Detection**: Finds words in screenshots rather than just DOM text
- Configurable timeout settings
- Simple popup interface with visual area selection
- Real-time word counting with area-specific search
- Fallback to DOM search if OCR fails
- Confidence scoring for OCR results
- Lightweight and fast

## Development

### Prerequisites

- Node.js (v14 or higher)
- npm

### Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

### Build Scripts

The project includes a comprehensive build system with the following commands:

#### Basic Build Commands

```bash
# Default build - creates dist/ directory with extension files
npm run build

# Development build
npm run build:dev

# Production build (optimized)
npm run build:prod

# Production build with ZIP package for Chrome Web Store
npm run build:prod -- --package
```

#### Utility Commands

```bash
# Create icons only
npm run build:icons

# Clean build directory
npm run clean

# Create ZIP package from existing build
npm run package
```

#### Advanced Build Options

You can also run the build script directly with various options:

```bash
# Show help
node build.js --help

# Clean and rebuild
node build.js --clean --prod

# Development build
node build.js --dev

# Production build with packaging
node build.js --prod --package
```

### Build Output

- **dist/**: Contains the built extension files ready for loading into Chrome
- **word-tracker-extension-v1.0.zip**: ZIP package ready for Chrome Web Store submission

### Loading the Extension in Chrome

1. Run `npm run build` to create the extension files
2. Open Chrome and go to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the `dist/` directory
5. The extension should now appear in your extensions list

### Using the Screenshot-based OCR System

1. Click the extension icon to open the popup
2. Enter a target word to search for
3. (Optional) Click "Select Area" to choose a specific region
4. Click "Start Tracking" to begin OCR-based word detection
5. The extension will:
   - Capture a screenshot of the selected area (or full page)
   - Process the image using OCR technology
   - Search for the target word in the extracted text
   - Display results with confidence scores
6. If OCR fails, the system automatically falls back to DOM text search

### Using the Area Selector

1. Click the extension icon to open the popup
2. Click "Select Area" button to start area selection mode
3. On the webpage, click and drag to select the area you want to search within
4. The selected area will be highlighted with a blue dashed border
5. Release the mouse to confirm the selection
6. Press ESC to cancel area selection
7. Use "Clear" button to remove area selection and search the full page
8. The area selection is saved and will persist across sessions

### Publishing to Chrome Web Store

1. Run `npm run build:prod -- --package` to create the ZIP file
2. Go to the [Chrome Web Store Developer Dashboard](https://chrome.google.com/webstore/devconsole/)
3. Upload the generated ZIP file
4. Fill in the required metadata and submit for review

## Project Structure

```
├── manifest.json       # Extension manifest
├── popup.html         # Extension popup UI
├── popup.js           # Popup functionality
├── content.js         # Content script
├── build.js           # Build script
├── create-icons.js    # Icon generation script
├── package.json       # Node.js dependencies and scripts
└── dist/              # Built extension (generated)
    ├── manifest.json
    ├── popup.html
    ├── popup.js
    ├── content.js
    └── images/
        ├── icon16.png
        ├── icon48.png
        └── icon128.png
```

## OCR Technology

The extension uses advanced screenshot-based OCR (Optical Character Recognition) to detect words in visual content:

### OCR Features
- **Image Processing**: Captures screenshots of selected areas or full pages
- **Text Recognition**: Extracts text from images, including content in canvas elements, images, and styled text
- **Confidence Scoring**: Provides accuracy ratings for detected text
- **Fallback System**: Automatically switches to DOM text search if OCR fails
- **Performance Optimized**: Efficient processing with minimal impact on page performance

### OCR Workflow
1. **Screenshot Capture**: Uses Chrome's tab capture API to get high-quality screenshots
2. **Area Cropping**: Crops images to selected regions for focused analysis
3. **Text Extraction**: Processes images using OCR algorithms to extract text
4. **Word Matching**: Searches extracted text for target words with fuzzy matching
5. **Result Reporting**: Returns word counts, positions, and confidence scores

### Technical Implementation
- **Background Script**: Handles screenshot capture using Chrome APIs
- **Content Script**: Manages area selection and image processing
- **OCR Engine**: Custom implementation with pattern recognition
- **Fallback Support**: DOM-based text search as backup

## AreaSelector API

The extension includes a custom AreaSelector API that provides:

### Features
- **Visual Selection**: Click and drag to select areas on any webpage
- **Overlay Interface**: Semi-transparent overlay with selection box
- **Keyboard Support**: ESC key to cancel selection
- **Persistent Storage**: Selected areas are saved across sessions
- **Responsive Design**: Works with different screen sizes and zoom levels

### API Methods
- `startSelection()`: Initiates area selection mode
- `getSelectedArea()`: Returns current selected area coordinates
- `clearSelectedArea()`: Removes current area selection
- `cleanup()`: Removes overlay and resets state

### Usage in Content Script
```javascript
// Start area selection
const area = await window.wordTrackerAreaSelector.startSelection();

// Get current selection
const currentArea = window.wordTrackerAreaSelector.getSelectedArea();

// Clear selection
window.wordTrackerAreaSelector.clearSelectedArea();
```

## Build Script Features

The build script (`build.js`) provides:

- ✅ Manifest validation
- 📦 Automatic icon generation
- 🗂️ File copying and organization
- 🔧 Production optimization
- 📦 ZIP packaging for Chrome Web Store
- 🧹 Clean build directory
- 📊 Build information display
- ⚠️ Error handling and validation

## Notes

- Icons are currently generated as simple placeholder images
- For production use, replace the generated icons with proper PNG files
- The build script automatically validates the manifest.json file
- All build outputs are placed in the `dist/` directory

## License

ISC
