# Word Tracker Chrome Extension

A Chrome extension that tracks occurrences of target words with configurable timeout.

## Features

- Track specific words on web pages
- Configurable timeout settings
- Simple popup interface
- Lightweight and fast

## Development

### Prerequisites

- Node.js (v14 or higher)
- npm

### Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

### Build Scripts

The project includes a comprehensive build system with the following commands:

#### Basic Build Commands

```bash
# Default build - creates dist/ directory with extension files
npm run build

# Development build
npm run build:dev

# Production build (optimized)
npm run build:prod

# Production build with ZIP package for Chrome Web Store
npm run build:prod -- --package
```

#### Utility Commands

```bash
# Create icons only
npm run build:icons

# Clean build directory
npm run clean

# Create ZIP package from existing build
npm run package
```

#### Advanced Build Options

You can also run the build script directly with various options:

```bash
# Show help
node build.js --help

# Clean and rebuild
node build.js --clean --prod

# Development build
node build.js --dev

# Production build with packaging
node build.js --prod --package
```

### Build Output

- **dist/**: Contains the built extension files ready for loading into Chrome
- **word-tracker-extension-v1.0.zip**: ZIP package ready for Chrome Web Store submission

### Loading the Extension in Chrome

1. Run `npm run build` to create the extension files
2. Open Chrome and go to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the `dist/` directory
5. The extension should now appear in your extensions list

### Publishing to Chrome Web Store

1. Run `npm run build:prod -- --package` to create the ZIP file
2. Go to the [Chrome Web Store Developer Dashboard](https://chrome.google.com/webstore/devconsole/)
3. Upload the generated ZIP file
4. Fill in the required metadata and submit for review

## Project Structure

```
├── manifest.json       # Extension manifest
├── popup.html         # Extension popup UI
├── popup.js           # Popup functionality
├── content.js         # Content script
├── build.js           # Build script
├── create-icons.js    # Icon generation script
├── package.json       # Node.js dependencies and scripts
└── dist/              # Built extension (generated)
    ├── manifest.json
    ├── popup.html
    ├── popup.js
    ├── content.js
    └── images/
        ├── icon16.png
        ├── icon48.png
        └── icon128.png
```

## Build Script Features

The build script (`build.js`) provides:

- ✅ Manifest validation
- 📦 Automatic icon generation
- 🗂️ File copying and organization
- 🔧 Production optimization
- 📦 ZIP packaging for Chrome Web Store
- 🧹 Clean build directory
- 📊 Build information display
- ⚠️ Error handling and validation

## Notes

- Icons are currently generated as simple placeholder images
- For production use, replace the generated icons with proper PNG files
- The build script automatically validates the manifest.json file
- All build outputs are placed in the `dist/` directory

## License

ISC
