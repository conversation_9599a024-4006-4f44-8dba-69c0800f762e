// Word Tracker Extension Content Script
console.log('Word Tracker extension loaded');

// AreaSelector API Implementation
class AreaSelector {
  constructor() {
    this.isSelecting = false;
    this.selectedArea = null;
    this.overlay = null;
    this.selectionBox = null;
    this.startPoint = null;
    this.endPoint = null;
  }

  // Start area selection mode
  startSelection() {
    if (this.isSelecting) return;

    this.isSelecting = true;
    this.createOverlay();
    this.addEventListeners();
    document.body.style.cursor = 'crosshair';

    return new Promise((resolve) => {
      this.resolveSelection = resolve;
    });
  }

  // Create overlay for area selection
  createOverlay() {
    this.overlay = document.createElement('div');
    this.overlay.id = 'word-tracker-overlay';
    this.overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0.3);
      z-index: 999999;
      cursor: crosshair;
    `;

    this.selectionBox = document.createElement('div');
    this.selectionBox.id = 'word-tracker-selection-box';
    this.selectionBox.style.cssText = `
      position: absolute;
      border: 2px dashed #4285f4;
      background: rgba(66, 133, 244, 0.1);
      display: none;
      pointer-events: none;
    `;

    this.overlay.appendChild(this.selectionBox);
    document.body.appendChild(this.overlay);
  }

  // Add event listeners for area selection
  addEventListeners() {
    this.mouseDownHandler = this.onMouseDown.bind(this);
    this.mouseMoveHandler = this.onMouseMove.bind(this);
    this.mouseUpHandler = this.onMouseUp.bind(this);
    this.keyDownHandler = this.onKeyDown.bind(this);

    this.overlay.addEventListener('mousedown', this.mouseDownHandler);
    document.addEventListener('mousemove', this.mouseMoveHandler);
    document.addEventListener('mouseup', this.mouseUpHandler);
    document.addEventListener('keydown', this.keyDownHandler);
  }

  // Remove event listeners
  removeEventListeners() {
    if (this.overlay) {
      this.overlay.removeEventListener('mousedown', this.mouseDownHandler);
    }
    document.removeEventListener('mousemove', this.mouseMoveHandler);
    document.removeEventListener('mouseup', this.mouseUpHandler);
    document.removeEventListener('keydown', this.keyDownHandler);
  }

  // Handle mouse down event
  onMouseDown(e) {
    e.preventDefault();
    this.startPoint = {
      x: e.clientX,
      y: e.clientY
    };
    this.selectionBox.style.display = 'block';
    this.updateSelectionBox(e.clientX, e.clientY);
  }

  // Handle mouse move event
  onMouseMove(e) {
    if (!this.startPoint) return;
    e.preventDefault();
    this.updateSelectionBox(e.clientX, e.clientY);
  }

  // Handle mouse up event
  onMouseUp(e) {
    if (!this.startPoint) return;
    e.preventDefault();

    this.endPoint = {
      x: e.clientX,
      y: e.clientY
    };

    const rect = this.getSelectionRect();
    if (rect.width > 10 && rect.height > 10) {
      this.selectedArea = rect;
      this.finishSelection(rect);
    } else {
      this.cancelSelection();
    }
  }

  // Handle key down event (ESC to cancel)
  onKeyDown(e) {
    if (e.key === 'Escape') {
      this.cancelSelection();
    }
  }

  // Update selection box position and size
  updateSelectionBox(currentX, currentY) {
    if (!this.startPoint) return;

    const left = Math.min(this.startPoint.x, currentX);
    const top = Math.min(this.startPoint.y, currentY);
    const width = Math.abs(currentX - this.startPoint.x);
    const height = Math.abs(currentY - this.startPoint.y);

    this.selectionBox.style.left = left + 'px';
    this.selectionBox.style.top = top + 'px';
    this.selectionBox.style.width = width + 'px';
    this.selectionBox.style.height = height + 'px';
  }

  // Get selection rectangle
  getSelectionRect() {
    const left = Math.min(this.startPoint.x, this.endPoint.x);
    const top = Math.min(this.startPoint.y, this.endPoint.y);
    const width = Math.abs(this.endPoint.x - this.startPoint.x);
    const height = Math.abs(this.endPoint.y - this.startPoint.y);

    return { left, top, width, height };
  }

  // Finish selection
  finishSelection(rect) {
    this.cleanup();
    if (this.resolveSelection) {
      this.resolveSelection(rect);
    }
  }

  // Cancel selection
  cancelSelection() {
    this.cleanup();
    if (this.resolveSelection) {
      this.resolveSelection(null);
    }
  }

  // Clean up overlay and reset state
  cleanup() {
    this.isSelecting = false;
    this.startPoint = null;
    this.endPoint = null;
    document.body.style.cursor = '';

    this.removeEventListeners();

    if (this.overlay) {
      document.body.removeChild(this.overlay);
      this.overlay = null;
      this.selectionBox = null;
    }
  }

  // Get currently selected area
  getSelectedArea() {
    return this.selectedArea;
  }

  // Clear selected area
  clearSelectedArea() {
    this.selectedArea = null;
  }
}

// Global instances
window.wordTrackerAreaSelector = new AreaSelector();
window.wordTrackerOCR = new OCRWordDetector();

// Screenshot-based word detection
class ScreenshotWordDetector {
  constructor() {
    this.ocrDetector = window.wordTrackerOCR;
  }

  // Detect words in screenshot of selected area
  async detectWordsInArea(targetWord, selectedArea) {
    try {
      let screenshot;

      if (selectedArea) {
        // Capture specific area
        screenshot = await this.captureAreaScreenshot(selectedArea);
      } else {
        // Capture full visible tab
        screenshot = await this.captureFullScreenshot();
      }

      // Perform OCR on screenshot
      const ocrResult = await this.ocrDetector.recognizeText(screenshot);

      // Find target word in OCR results
      const wordResult = this.ocrDetector.findWordInText(ocrResult, targetWord);

      return {
        success: true,
        count: wordResult.count,
        matches: wordResult.matches,
        wordDetails: wordResult.wordDetails,
        confidence: wordResult.confidence,
        method: 'OCR',
        area: selectedArea ? 'selected area' : 'full page'
      };
    } catch (error) {
      console.error('Screenshot word detection failed:', error);
      return {
        success: false,
        error: error.message,
        count: 0,
        matches: [],
        method: 'OCR'
      };
    }
  }

  // Capture screenshot of specific area
  async captureAreaScreenshot(area) {
    try {
      // First get full page screenshot
      const fullScreenshot = await this.captureFullScreenshot();

      // Crop to selected area
      return await this.cropImageToArea(fullScreenshot, area);
    } catch (error) {
      console.error('Area screenshot capture failed:', error);
      throw error;
    }
  }

  // Capture full page screenshot
  async captureFullScreenshot() {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({
        action: 'captureVisibleTab',
        options: { format: 'png', quality: 100 }
      }, (response) => {
        if (response.success) {
          resolve(response.dataUrl);
        } else {
          reject(new Error(response.error));
        }
      });
    });
  }

  // Crop image to specific area
  async cropImageToArea(imageDataUrl, area) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Set canvas size to selected area
        canvas.width = area.width;
        canvas.height = area.height;

        // Draw cropped portion of image
        ctx.drawImage(
          img,
          area.left, area.top, area.width, area.height, // Source rectangle
          0, 0, area.width, area.height // Destination rectangle
        );

        // Convert to blob
        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to crop image'));
          }
        }, 'image/png');
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = imageDataUrl;
    });
  }
}

// Global screenshot detector instance
window.wordTrackerScreenshotDetector = new ScreenshotWordDetector();

// Listen for messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'startAreaSelection') {
    window.wordTrackerAreaSelector.startSelection().then((area) => {
      sendResponse({ success: true, area: area });
    });
    return true; // Keep message channel open for async response
  }

  if (request.action === 'clearAreaSelection') {
    window.wordTrackerAreaSelector.clearSelectedArea();
    sendResponse({ success: true });
  }

  if (request.action === 'getSelectedArea') {
    const area = window.wordTrackerAreaSelector.getSelectedArea();
    sendResponse({ success: true, area: area });
  }

  if (request.action === 'detectWordsInScreenshot') {
    window.wordTrackerScreenshotDetector.detectWordsInArea(
      request.targetWord,
      request.selectedArea
    ).then((result) => {
      sendResponse(result);
    }).catch((error) => {
      sendResponse({
        success: false,
        error: error.message,
        count: 0,
        matches: []
      });
    });
    return true; // Keep message channel open for async response
  }
});