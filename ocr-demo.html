<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OCR Word Tracker Demo - Visual Content</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .visual-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        .canvas-demo {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .styled-text {
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            padding: 10px;
        }
        .image-text {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 18px;
            text-align: center;
            margin: 15px 0;
        }
        .rotated-text {
            transform: rotate(-5deg);
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 8px;
            display: inline-block;
            margin: 10px;
            font-weight: bold;
        }
        .overlay-text {
            position: relative;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100"><rect width="100" height="100" fill="%23f39c12"/><text x="50" y="50" text-anchor="middle" dy=".3em" fill="white" font-size="12">DEMO</text></svg>') repeat;
            padding: 20px;
            border-radius: 8px;
        }
        .instructions {
            background: rgba(52, 152, 219, 0.8);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .word-highlight {
            background: #f1c40f;
            color: #2c3e50;
            padding: 3px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        canvas {
            border: 2px solid #34495e;
            border-radius: 8px;
            background: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 OCR Word Tracker Demo</h1>
            <p>Test screenshot-based word detection with visual content</p>
        </div>

        <div class="instructions">
            <h3>📋 OCR Testing Instructions:</h3>
            <ol>
                <li>Install the Word Tracker extension</li>
                <li>Try searching for words like: <span class="word-highlight">test</span>, <span class="word-highlight">demo</span>, <span class="word-highlight">OCR</span>, <span class="word-highlight">visual</span></li>
                <li>Select different areas below to test area-specific OCR</li>
                <li>Compare OCR results with DOM text search</li>
                <li>Notice how OCR can detect text in images and canvas elements</li>
            </ol>
        </div>

        <div class="visual-section">
            <h3>🎨 Styled Text Content</h3>
            <div class="styled-text">
                This is a DEMO of styled text that should be detected by OCR
            </div>
            <div class="rotated-text">
                Rotated TEST text for OCR detection
            </div>
            <p>Regular paragraph text with <span class="word-highlight">highlighted</span> words for testing OCR accuracy.</p>
        </div>

        <div class="visual-section">
            <h3>🖼️ Image-like Content</h3>
            <div class="image-text">
                VISUAL CONTENT TEST
                This text simulates image-based content
                OCR should detect these words: demo, test, visual
            </div>
            <div class="overlay-text">
                <h4>Overlay Text Demo</h4>
                <p>This section has background patterns that make OCR more challenging. The word "test" appears here multiple times for demo purposes.</p>
            </div>
        </div>

        <div class="visual-section">
            <h3>📊 Canvas Content</h3>
            <div class="canvas-demo">
                <canvas id="demoCanvas" width="600" height="200"></canvas>
                <p style="color: #2c3e50; margin-top: 10px;">
                    <strong>Canvas Element:</strong> The canvas above contains text that can only be detected through OCR, not DOM text search.
                </p>
            </div>
        </div>

        <div class="visual-section">
            <h3>🔤 Mixed Content Test</h3>
            <p>This section contains a mix of regular DOM text and visual elements:</p>
            <ul>
                <li>Regular <span class="word-highlight">test</span> text in DOM</li>
                <li>Styled <span class="styled-text">demo</span> content</li>
                <li>Canvas-based visual text (see above)</li>
                <li>Background pattern text</li>
            </ul>
            <div style="background: #9b59b6; color: white; padding: 15px; border-radius: 8px; text-align: center; font-size: 20px; font-weight: bold;">
                VISUAL TEST CONTENT FOR OCR DEMO
            </div>
        </div>

        <div class="visual-section">
            <h3>📈 Performance Test Area</h3>
            <p>Select this entire section to test OCR performance on a larger area with mixed content types.</p>
            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin: 15px 0;">
                <div style="background: #e67e22; padding: 15px; border-radius: 8px; text-align: center; color: white;">
                    <strong>TEST</strong><br>Section 1
                </div>
                <div style="background: #27ae60; padding: 15px; border-radius: 8px; text-align: center; color: white;">
                    <strong>DEMO</strong><br>Section 2
                </div>
                <div style="background: #8e44ad; padding: 15px; border-radius: 8px; text-align: center; color: white;">
                    <strong>OCR</strong><br>Section 3
                </div>
            </div>
        </div>

        <div class="visual-section">
            <h3>🎯 OCR Challenge Area</h3>
            <p>This area contains text that's challenging for OCR:</p>
            <div style="font-family: 'Times New Roman', serif; font-style: italic; transform: skew(-5deg); background: rgba(0,0,0,0.7); padding: 20px; border-radius: 8px;">
                Italic skewed text: "test demo visual OCR challenge"
            </div>
            <div style="font-size: 12px; opacity: 0.8; margin: 10px 0;">
                Small text: test demo OCR detection accuracy
            </div>
            <div style="letter-spacing: 5px; font-weight: bold; margin: 10px 0;">
                S P A C E D   T E S T   T E X T
            </div>
        </div>
    </div>

    <script>
        // Draw text on canvas for OCR testing
        document.addEventListener('DOMContentLoaded', function() {
            const canvas = document.getElementById('demoCanvas');
            const ctx = canvas.getContext('2d');
            
            // Set canvas background
            ctx.fillStyle = '#ecf0f1';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw title
            ctx.fillStyle = '#2c3e50';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Canvas OCR Demo Test', canvas.width / 2, 40);
            
            // Draw subtitle
            ctx.font = '18px Arial';
            ctx.fillText('This text is drawn on canvas and can only be detected by OCR', canvas.width / 2, 70);
            
            // Draw test words
            ctx.font = 'bold 20px Arial';
            ctx.fillStyle = '#e74c3c';
            ctx.textAlign = 'left';
            ctx.fillText('TEST', 50, 120);
            
            ctx.fillStyle = '#27ae60';
            ctx.fillText('DEMO', 150, 120);
            
            ctx.fillStyle = '#3498db';
            ctx.fillText('VISUAL', 250, 120);
            
            ctx.fillStyle = '#9b59b6';
            ctx.fillText('OCR', 350, 120);
            
            // Draw additional text
            ctx.fillStyle = '#34495e';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Canvas-based content requires screenshot OCR for word detection', canvas.width / 2, 160);
            
            console.log('Canvas demo content created for OCR testing');
        });
    </script>
</body>
</html>
